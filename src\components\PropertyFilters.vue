<template>
  <div class="property-filters">
    <div class="filters-header">
      <h4 class="filters-title">🔍 Фильтры</h4>
      <button 
        type="button" 
        class="clear-filters-btn"
        @click="clearAllFilters"
        :disabled="!hasActiveFilters"
      >
        Очистить
      </button>
    </div>
    
    <div class="filters-content">
      <!-- Тип сделки -->
      <div class="filter-group">
        <label>Тип сделки</label>
        <select v-model="localFilters.dealType">
          <option value="">Все</option>
          <option value="sale">Продажа</option>
          <option value="rent">Аренда</option>
        </select>
      </div>
      
      <!-- Тип недвижимости -->
      <div class="filter-group">
        <label>Тип недвижимости</label>
        <select v-model="localFilters.propertyType">
          <option value="">Все</option>
          <option value="1+1">1+1 (евро)</option>
          <option value="2+1">2+1 (две спальни и зал)</option>
          <option value="3+1">3+1 (три спальни и зал)</option>
          <option value="studio">Студия</option>
          <option value="house">Дом</option>
          <option value="commercial">Коммерческая</option>
        </select>
      </div>
      
      <!-- Статус доступности (только для аренды) -->
      <div v-if="localFilters.dealType === 'rent'" class="filter-group">
        <label>Статус доступности</label>
        <select v-model="localFilters.availabilityStatus">
          <option value="">Все</option>
          <option value="available">Свободна</option>
          <option value="occupied">Занята</option>
        </select>
      </div>
      
      <!-- Цена -->
      <div class="filter-group">
        <label>Цена</label>
        <div class="price-range">
          <input 
            v-model.number="localFilters.priceFrom"
            type="number" 
            placeholder="От"
            min="0"
          >
          <span class="range-separator">—</span>
          <input 
            v-model.number="localFilters.priceTo"
            type="number" 
            placeholder="До"
            min="0"
          >
        </div>
      </div>
      
      <!-- Период дат (для поиска свободных периодов аренды) -->
      <div v-if="localFilters.dealType === 'rent'" class="filter-group">
        <label>Период поиска свободных дат</label>
        <div class="date-range">
          <input 
            v-model="localFilters.dateFrom"
            type="date"
          >
          <span class="range-separator">—</span>
          <input 
            v-model="localFilters.dateTo"
            type="date"
          >
        </div>
      </div>
      
      <!-- Теги/Удобства -->
      <div class="filter-group">
        <label>Удобства</label>
        <div class="tags-filter">
          <div class="selected-filter-tags">
            <div 
              v-for="tag in localFilters.tags" 
              :key="tag"
              class="filter-tag selected"
            >
              {{ tag }}
              <button 
                type="button" 
                class="remove-filter-tag"
                @click="removeFilterTag(tag)"
              >
                ×
              </button>
            </div>
          </div>
          
          <div class="available-filter-tags">
            <button
              v-for="tag in availableFilterTags"
              :key="tag"
              type="button"
              class="filter-tag available"
              @click="addFilterTag(tag)"
            >
              {{ tag }}
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Результаты фильтрации -->
    <div class="filter-results">
      <div class="results-count">
        Найдено: {{ filteredCount }} объектов
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useAgentStore } from '@/stores/agent'

const agentStore = useAgentStore()

const localFilters = ref({
  dealType: '',
  propertyType: '',
  availabilityStatus: '',
  priceFrom: null,
  priceTo: null,
  dateFrom: null,
  dateTo: null,
  tags: []
})

// Синхронизируем локальные фильтры с store
watch(localFilters, (newFilters) => {
  agentStore.updateFilters(newFilters)
}, { deep: true })

// Инициализируем локальные фильтры из store
watch(() => agentStore.filters, (storeFilters) => {
  localFilters.value = { ...storeFilters }
}, { immediate: true })

const availableFilterTags = computed(() => {
  return agentStore.availableTags.filter(tag => 
    !localFilters.value.tags.includes(tag)
  ).slice(0, 8) // Показываем только первые 8 для компактности
})

const filteredCount = computed(() => {
  return agentStore.filteredProperties.length
})

const hasActiveFilters = computed(() => {
  const filters = localFilters.value
  return !!(
    filters.dealType ||
    filters.propertyType ||
    filters.availabilityStatus ||
    filters.priceFrom ||
    filters.priceTo ||
    filters.dateFrom ||
    filters.dateTo ||
    filters.tags.length > 0
  )
})

const addFilterTag = (tag) => {
  if (!localFilters.value.tags.includes(tag)) {
    localFilters.value.tags = [...localFilters.value.tags, tag]
  }
}

const removeFilterTag = (tag) => {
  localFilters.value.tags = localFilters.value.tags.filter(t => t !== tag)
}

const clearAllFilters = () => {
  localFilters.value = {
    dealType: '',
    propertyType: '',
    availabilityStatus: '',
    priceFrom: null,
    priceTo: null,
    dateFrom: null,
    dateTo: null,
    tags: []
  }
  agentStore.clearFilters()
}
</script>

<style scoped>
.property-filters {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 1.5rem;
  backdrop-filter: blur(10px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  margin-bottom: 1.5rem;
}

.filters-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #eee;
}

.filters-title {
  margin: 0;
  color: #333;
  font-size: 1.1rem;
}

.clear-filters-btn {
  padding: 0.4rem 0.8rem;
  background: #f44336;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.clear-filters-btn:hover:not(:disabled) {
  background: #d32f2f;
  transform: translateY(-1px);
}

.clear-filters-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
}

.filters-content {
  display: grid;
  gap: 1rem;
}

.filter-group {
  display: flex;
  flex-direction: column;
}

.filter-group label {
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #333;
  font-size: 0.9rem;
}

.filter-group select,
.filter-group input {
  padding: 0.6rem;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 0.9rem;
  transition: border-color 0.3s ease;
}

.filter-group select:focus,
.filter-group input:focus {
  outline: none;
  border-color: #4caf50;
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}

.price-range,
.date-range {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.price-range input,
.date-range input {
  flex: 1;
}

.range-separator {
  color: #666;
  font-weight: 500;
}

.tags-filter {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.selected-filter-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.3rem;
  min-height: 32px;
  padding: 0.5rem;
  background: #f8f9fa;
  border-radius: 6px;
  align-items: center;
}

.available-filter-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.3rem;
}

.filter-tag {
  display: inline-flex;
  align-items: center;
  padding: 0.3rem 0.6rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-tag.selected {
  background: #4caf50;
  color: white;
  gap: 0.3rem;
}

.filter-tag.available {
  background: white;
  color: #555;
  border: 1px solid #ddd;
}

.filter-tag.available:hover {
  background: #4caf50;
  color: white;
  border-color: #4caf50;
  transform: translateY(-1px);
}

.remove-filter-tag {
  background: none;
  border: none;
  color: white;
  font-size: 0.9rem;
  cursor: pointer;
  padding: 0;
  width: 14px;
  height: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.remove-filter-tag:hover {
  background: rgba(255, 255, 255, 0.2);
}

.filter-results {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #eee;
}

.results-count {
  font-size: 0.9rem;
  color: #666;
  text-align: center;
  font-weight: 500;
}

@media (max-width: 768px) {
  .filters-header {
    flex-direction: column;
    gap: 0.5rem;
    align-items: stretch;
  }
  
  .price-range,
  .date-range {
    flex-direction: column;
    gap: 0.3rem;
  }
  
  .range-separator {
    text-align: center;
  }
}
</style>
