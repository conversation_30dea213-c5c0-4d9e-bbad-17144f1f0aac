<template>
  <div class="tags-selector">
    <label class="tags-label">{{ label }}</label>
    
    <!-- Выбранные теги -->
    <div v-if="selectedTags.length > 0" class="selected-tags">
      <div 
        v-for="tag in selectedTags" 
        :key="tag"
        class="tag selected-tag"
      >
        {{ tag }}
        <button 
          type="button" 
          class="remove-tag-btn"
          @click="removeTag(tag)"
        >
          ×
        </button>
      </div>
    </div>
    
    <!-- Поле для добавления нового тега -->
    <div class="add-tag-section">
      <input 
        v-model="newTag"
        type="text" 
        class="tag-input"
        placeholder="Добавить новый тег..."
        @keyup.enter="addNewTag"
        @input="filterAvailableTags"
      >
      <button 
        type="button" 
        class="add-tag-btn"
        @click="addNewTag"
        :disabled="!newTag.trim()"
      >
        Добавить
      </button>
    </div>
    
    <!-- Доступные теги для выбора -->
    <div v-if="filteredAvailableTags.length > 0" class="available-tags">
      <div class="available-tags-title">Популярные теги:</div>
      <div class="tags-grid">
        <button
          v-for="tag in filteredAvailableTags"
          :key="tag"
          type="button"
          class="tag available-tag"
          @click="selectTag(tag)"
        >
          {{ tag }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useAgentStore } from '@/stores/agent'

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  label: {
    type: String,
    default: 'Теги/Удобства'
  }
})

const emit = defineEmits(['update:modelValue'])

const agentStore = useAgentStore()
const newTag = ref('')

const selectedTags = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const availableTags = computed(() => agentStore.availableTags)

const filteredAvailableTags = ref([])

const filterAvailableTags = () => {
  const searchTerm = newTag.value.toLowerCase()
  
  if (!searchTerm) {
    // Показываем только теги, которые еще не выбраны
    filteredAvailableTags.value = availableTags.value.filter(
      tag => !selectedTags.value.includes(tag)
    ).slice(0, 10) // Ограничиваем количество для удобства
  } else {
    // Фильтруем по поисковому запросу
    filteredAvailableTags.value = availableTags.value.filter(
      tag => tag.toLowerCase().includes(searchTerm) && !selectedTags.value.includes(tag)
    )
  }
}

const selectTag = (tag) => {
  if (!selectedTags.value.includes(tag)) {
    selectedTags.value = [...selectedTags.value, tag]
  }
  newTag.value = ''
  filterAvailableTags()
}

const removeTag = (tag) => {
  selectedTags.value = selectedTags.value.filter(t => t !== tag)
  filterAvailableTags()
}

const addNewTag = () => {
  const tag = newTag.value.trim()
  if (tag && !selectedTags.value.includes(tag)) {
    selectedTags.value = [...selectedTags.value, tag]
    
    // Добавляем новый тег в список доступных, если его там нет
    if (!availableTags.value.includes(tag)) {
      agentStore.availableTags.push(tag)
    }
  }
  newTag.value = ''
  filterAvailableTags()
}

// Инициализируем фильтрованные теги при монтировании
watch(() => selectedTags.value, () => {
  filterAvailableTags()
}, { immediate: true })
</script>

<style scoped>
.tags-selector {
  margin-bottom: 1rem;
}

.tags-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #333;
}

.selected-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
  padding: 0.5rem;
  background: #f8f9fa;
  border-radius: 8px;
  min-height: 40px;
  align-items: center;
}

.tag {
  display: inline-flex;
  align-items: center;
  padding: 0.3rem 0.6rem;
  border-radius: 16px;
  font-size: 0.8rem;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.selected-tag {
  background: #4caf50;
  color: white;
  gap: 0.3rem;
}

.remove-tag-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  padding: 0;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.remove-tag-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.add-tag-section {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.tag-input {
  flex: 1;
  padding: 0.6rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 0.9rem;
  transition: border-color 0.3s ease;
}

.tag-input:focus {
  outline: none;
  border-color: #4caf50;
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}

.add-tag-btn {
  padding: 0.6rem 1rem;
  background: #4caf50;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.add-tag-btn:hover:not(:disabled) {
  background: #45a049;
  transform: translateY(-1px);
}

.add-tag-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
}

.available-tags {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 1rem;
  background: #fafafa;
}

.available-tags-title {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.tags-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 0.3rem;
}

.available-tag {
  background: white;
  color: #555;
  border: 1px solid #ddd;
}

.available-tag:hover {
  background: #4caf50;
  color: white;
  border-color: #4caf50;
  transform: translateY(-1px);
}

@media (max-width: 768px) {
  .add-tag-section {
    flex-direction: column;
  }
  
  .tags-grid {
    gap: 0.2rem;
  }
  
  .tag {
    font-size: 0.75rem;
    padding: 0.2rem 0.5rem;
  }
}
</style>
