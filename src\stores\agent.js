import { defineStore } from 'pinia'

export const useAgentStore = defineStore('agent', {
  state: () => ({
    agent: {
      id: 1,
      name: '<PERSON>в<PERSON><PERSON>етров',
      email: '<EMAIL>',
      phone: '+7 (999) 123-45-67',
      region: 'moscow',
      avatar: '👤',
      personalLink: 'https://realestate.com/agent/ivan-petrov',
    },

    properties: [
      {
        id: 1,
        title: 'Квартира в центре',
        address: 'Москва, ул. Тверская, 1',
        price: 15000000,
        dealType: 'sale',
        propertyType: '2+1',
        description: 'Прекрасная квартира в самом центре Москвы',
        coordinates: { lat: 55.7558, lng: 37.6176 },
        status: 'paid',
        createdAt: new Date('2024-01-15'),
        // Теги/удобства
        tags: ['Ванна с окном', 'Посудомойка', 'Кондиционер', 'Балкон'],
        // Фотографии
        photos: [
          { id: 1, url: '/placeholder-1.jpg', name: 'Гостиная' },
          { id: 2, url: '/placeholder-2.jpg', name: 'Кухня' },
          { id: 3, url: '/placeholder-3.jpg', name: 'Спальня' },
        ],
        rental: null,
      },
      {
        id: 2,
        title: 'Студия на Арбате',
        address: 'Москва, ул. Арбат, 25',
        price: 80000,
        dealType: 'rent',
        propertyType: 'studio',
        description: 'Уютная студия для молодой семьи',
        coordinates: { lat: 55.7522, lng: 37.5927 },
        status: 'unpaid',
        createdAt: new Date('2024-02-10'),
        // Теги/удобства
        tags: ['Студия', 'Мебель', 'Интернет', 'Парковка'],
        // Фотографии
        photos: [
          { id: 1, url: '/placeholder-4.jpg', name: 'Общий вид' },
          { id: 2, url: '/placeholder-5.jpg', name: 'Кухонная зона' },
        ],
        // Статус доступности для аренды
        availabilityStatus: 'occupied', // 'available' или 'occupied'
        rental: {
          currentTenant: {
            name: 'Анна Иванова',
            phone: '+7 (999) 888-77-66',
            email: '<EMAIL>',
            passportData: '1234 567890',
          },
          rentPeriods: [
            {
              id: 1,
              startDate: new Date('2024-02-01'),
              endDate: new Date('2024-07-31'),
              monthlyRent: 80000,
              deposit: 160000,
              status: 'active',
            },
          ],
          calendar: {
            bookedDates: [
              {
                start: new Date('2024-02-01'),
                end: new Date('2024-07-31'),
                tenant: 'Анна Иванова',
              },
            ],
            availableDates: [{ start: new Date('2024-08-01'), end: new Date('2024-12-31') }],
          },
        },
      },
    ],

    mapSettings: {
      zoom: 12,
      center: { lat: 55.7558, lng: 37.6176 },
      view: 'map',
    },

    modals: {
      propertyModal: false,
      profileModal: false,
      editPropertyModal: false,
    },

    selectedProperty: null,

    tempCoordinates: null,

    // Множественный выбор недвижимости
    selectedProperties: [],

    // Фильтры
    filters: {
      dateFrom: null,
      dateTo: null,
      priceFrom: null,
      priceTo: null,
      tags: [],
      dealType: '',
      propertyType: '',
      availabilityStatus: '', // для арендной недвижимости
    },

    // Доступные теги для выбора
    availableTags: [
      'Ванна с окном',
      'Посудомойка',
      'Кондиционер',
      'Балкон',
      'Лоджия',
      'Мебель',
      'Интернет',
      'Парковка',
      'Лифт',
      'Консьерж',
      'Охрана',
      'Детская площадка',
      'Спортзал',
      'Бассейн',
      'Сауна',
      'Терраса',
      'Камин',
      'Гардеробная',
      'Кладовая',
      'Студия',
    ],
  }),

  getters: {
    propertiesByStatus: (state) => (status) => {
      return state.properties.filter((property) => property.status === status)
    },

    propertiesByDealType: (state) => (dealType) => {
      return state.properties.filter((property) => property.dealType === dealType)
    },

    totalProperties: (state) => state.properties.length,

    paidPropertiesCount: (state) => {
      return state.properties.filter((property) => property.status === 'paid').length
    },

    unpaidPropertiesCount: (state) => {
      return state.properties.filter((property) => property.status === 'unpaid').length
    },

    // Фильтрованная недвижимость
    filteredProperties: (state) => {
      let filtered = [...state.properties]

      // Фильтр по типу сделки
      if (state.filters.dealType) {
        filtered = filtered.filter((p) => p.dealType === state.filters.dealType)
      }

      // Фильтр по типу недвижимости
      if (state.filters.propertyType) {
        filtered = filtered.filter((p) => p.propertyType === state.filters.propertyType)
      }

      // Фильтр по цене
      if (state.filters.priceFrom) {
        filtered = filtered.filter((p) => p.price >= state.filters.priceFrom)
      }
      if (state.filters.priceTo) {
        filtered = filtered.filter((p) => p.price <= state.filters.priceTo)
      }

      // Фильтр по тегам
      if (state.filters.tags.length > 0) {
        filtered = filtered.filter((p) =>
          state.filters.tags.every((tag) => p.tags && p.tags.includes(tag)),
        )
      }

      // Фильтр по статусу доступности (только для аренды)
      if (state.filters.availabilityStatus) {
        filtered = filtered.filter(
          (p) => p.dealType === 'rent' && p.availabilityStatus === state.filters.availabilityStatus,
        )
      }

      return filtered
    },

    // Доступные арендные недвижимости
    availableRentals: (state) => {
      return state.properties.filter(
        (p) => p.dealType === 'rent' && p.availabilityStatus === 'available',
      )
    },

    // Занятые арендные недвижимости
    occupiedRentals: (state) => {
      return state.properties.filter(
        (p) => p.dealType === 'rent' && p.availabilityStatus === 'occupied',
      )
    },
  },

  actions: {
    updateAgent(agentData) {
      this.agent = { ...this.agent, ...agentData }
    },

    addProperty(propertyData) {
      const coordinates = this.tempCoordinates || this.generateRandomCoordinates()

      const newProperty = {
        id: Date.now(),
        ...propertyData,
        status: 'unpaid',
        createdAt: new Date(),
        coordinates: coordinates,
        tags: propertyData.tags || [],
        photos: propertyData.photos || [],
        availabilityStatus: propertyData.dealType === 'rent' ? 'available' : undefined,
        rental:
          propertyData.dealType === 'rent'
            ? {
                currentTenant: null,
                rentPeriods: [],
                calendar: {
                  bookedDates: [],
                  availableDates: [],
                },
              }
            : null,
      }

      this.properties.push(newProperty)
      this.tempCoordinates = null
      return newProperty
    },

    updateProperty(propertyId, propertyData) {
      const index = this.properties.findIndex((p) => p.id === propertyId)
      if (index !== -1) {
        this.properties[index] = { ...this.properties[index], ...propertyData }
      }
    },

    removeProperty(propertyId) {
      const index = this.properties.findIndex((p) => p.id === propertyId)
      if (index !== -1) {
        this.properties.splice(index, 1)
      }
    },

    payForProperty(propertyId) {
      const property = this.properties.find((p) => p.id === propertyId)
      if (property) {
        property.status = 'paid'
      }
    },

    updateMapSettings(settings) {
      this.mapSettings = { ...this.mapSettings, ...settings }
    },

    openModal(modalName) {
      this.modals[modalName] = true
    },

    closeModal(modalName) {
      this.modals[modalName] = false
    },

    closeAllModals() {
      Object.keys(this.modals).forEach((key) => {
        this.modals[key] = false
      })
    },

    generateRandomCoordinates() {
      const moscowBounds = {
        north: 55.9,
        south: 55.6,
        east: 37.8,
        west: 37.4,
      }

      return {
        lat: moscowBounds.south + Math.random() * (moscowBounds.north - moscowBounds.south),
        lng: moscowBounds.west + Math.random() * (moscowBounds.east - moscowBounds.west),
      }
    },

    addRentalPeriod(propertyId, rentalData) {
      const property = this.properties.find((p) => p.id === propertyId)
      if (property && property.dealType === 'rent') {
        if (!property.rental) {
          property.rental = {
            currentTenant: null,
            rentPeriods: [],
            calendar: {
              bookedDates: [],
              availableDates: [],
            },
          }
        }

        const newPeriod = {
          id: Date.now(),
          ...rentalData,
          status: 'active',
        }

        property.rental.rentPeriods.push(newPeriod)
        property.rental.currentTenant = rentalData.tenant

        property.rental.calendar.bookedDates.push({
          start: rentalData.startDate,
          end: rentalData.endDate,
          tenant: rentalData.tenant.name,
        })
      }
    },

    updateRentalPeriod(propertyId, periodId, updateData) {
      const property = this.properties.find((p) => p.id === propertyId)
      if (property && property.rental) {
        const periodIndex = property.rental.rentPeriods.findIndex((p) => p.id === periodId)
        if (periodIndex !== -1) {
          property.rental.rentPeriods[periodIndex] = {
            ...property.rental.rentPeriods[periodIndex],
            ...updateData,
          }
        }
      }
    },

    endRentalPeriod(propertyId, periodId) {
      const property = this.properties.find((p) => p.id === propertyId)
      if (property && property.rental) {
        const period = property.rental.rentPeriods.find((p) => p.id === periodId)
        if (period) {
          period.status = 'completed'
          period.endDate = new Date()
          property.rental.currentTenant = null
        }
      }
    },
    copyPersonalLink() {
      if (navigator.clipboard) {
        navigator.clipboard.writeText(this.agent.personalLink)
        return true
      }
      return false
    },

    // Методы для работы с тегами
    addTagToProperty(propertyId, tag) {
      const property = this.properties.find((p) => p.id === propertyId)
      if (property) {
        if (!property.tags) property.tags = []
        if (!property.tags.includes(tag)) {
          property.tags.push(tag)
        }
      }
    },

    removeTagFromProperty(propertyId, tag) {
      const property = this.properties.find((p) => p.id === propertyId)
      if (property && property.tags) {
        property.tags = property.tags.filter((t) => t !== tag)
      }
    },

    // Методы для работы с фотографиями
    addPhotoToProperty(propertyId, photo) {
      const property = this.properties.find((p) => p.id === propertyId)
      if (property) {
        if (!property.photos) property.photos = []
        if (property.photos.length < 10) {
          property.photos.push({
            id: Date.now(),
            ...photo,
          })
        }
      }
    },

    removePhotoFromProperty(propertyId, photoId) {
      const property = this.properties.find((p) => p.id === propertyId)
      if (property && property.photos) {
        property.photos = property.photos.filter((p) => p.id !== photoId)
      }
    },

    // Методы для работы с фильтрами
    updateFilters(newFilters) {
      this.filters = { ...this.filters, ...newFilters }
    },

    clearFilters() {
      this.filters = {
        dateFrom: null,
        dateTo: null,
        priceFrom: null,
        priceTo: null,
        tags: [],
        dealType: '',
        propertyType: '',
        availabilityStatus: '',
      }
    },

    // Методы для множественного выбора
    togglePropertySelection(propertyId) {
      const index = this.selectedProperties.indexOf(propertyId)
      if (index > -1) {
        this.selectedProperties.splice(index, 1)
      } else {
        this.selectedProperties.push(propertyId)
      }
    },

    selectAllProperties() {
      this.selectedProperties = this.properties.map((p) => p.id)
    },

    clearSelection() {
      this.selectedProperties = []
    },

    // Методы для управления статусом доступности
    updateAvailabilityStatus(propertyId, status) {
      const property = this.properties.find((p) => p.id === propertyId)
      if (property && property.dealType === 'rent') {
        property.availabilityStatus = status
      }
    },

    // Групповые операции
    deleteSelectedProperties() {
      this.properties = this.properties.filter((p) => !this.selectedProperties.includes(p.id))
      this.selectedProperties = []
    },

    updateSelectedPropertiesStatus(status) {
      this.selectedProperties.forEach((id) => {
        const property = this.properties.find((p) => p.id === id)
        if (property) {
          property.status = status
        }
      })
    },
  },
})
