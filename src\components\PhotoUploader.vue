<template>
  <div class="photo-uploader">
    <label class="photos-label">{{ label }}</label>
    
    <!-- Загруженные фотографии -->
    <div v-if="photos.length > 0" class="photos-grid">
      <div 
        v-for="(photo, index) in photos" 
        :key="photo.id"
        class="photo-item"
      >
        <div class="photo-preview">
          <img 
            :src="photo.url" 
            :alt="photo.name"
            class="photo-image"
            @error="handleImageError"
          >
          <div class="photo-overlay">
            <button 
              type="button"
              class="photo-action-btn edit-btn"
              @click="editPhoto(index)"
              title="Редактировать"
            >
              ✏️
            </button>
            <button 
              type="button"
              class="photo-action-btn delete-btn"
              @click="removePhoto(index)"
              title="Удалить"
            >
              🗑️
            </button>
          </div>
        </div>
        <div class="photo-name">{{ photo.name || `Фото ${index + 1}` }}</div>
      </div>
    </div>
    
    <!-- Кнопка загрузки -->
    <div v-if="photos.length < maxPhotos" class="upload-section">
      <input 
        ref="fileInput"
        type="file" 
        accept="image/*"
        multiple
        class="file-input"
        @change="handleFileSelect"
      >
      <button 
        type="button"
        class="upload-btn"
        @click="triggerFileSelect"
      >
        📷 Добавить фото ({{ photos.length }}/{{ maxPhotos }})
      </button>
    </div>
    
    <!-- Информация о лимите -->
    <div v-if="photos.length >= maxPhotos" class="limit-info">
      Достигнут лимит фотографий ({{ maxPhotos }})
    </div>
    
    <!-- Модальное окно редактирования фото -->
    <div v-if="editingPhoto !== null" class="modal" @click="closeEditModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h4>Редактировать фото</h4>
          <button class="close-btn" @click="closeEditModal">&times;</button>
        </div>
        <div class="modal-body">
          <div class="form-group">
            <label>Название фото</label>
            <input 
              v-model="editPhotoName"
              type="text" 
              placeholder="Например: Гостиная, Кухня, Спальня..."
            >
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-outline" @click="closeEditModal">
            Отмена
          </button>
          <button type="button" class="btn btn-primary" @click="savePhotoEdit">
            Сохранить
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  label: {
    type: String,
    default: 'Фотографии'
  },
  maxPhotos: {
    type: Number,
    default: 10
  }
})

const emit = defineEmits(['update:modelValue'])

const fileInput = ref(null)
const editingPhoto = ref(null)
const editPhotoName = ref('')

const photos = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const triggerFileSelect = () => {
  fileInput.value?.click()
}

const handleFileSelect = (event) => {
  const files = Array.from(event.target.files)
  const remainingSlots = props.maxPhotos - photos.value.length
  const filesToProcess = files.slice(0, remainingSlots)
  
  filesToProcess.forEach(file => {
    if (file.type.startsWith('image/')) {
      const reader = new FileReader()
      reader.onload = (e) => {
        const newPhoto = {
          id: Date.now() + Math.random(),
          url: e.target.result,
          name: file.name.split('.')[0],
          file: file
        }
        photos.value = [...photos.value, newPhoto]
      }
      reader.readAsDataURL(file)
    }
  })
  
  // Очищаем input для возможности повторного выбора тех же файлов
  event.target.value = ''
}

const removePhoto = (index) => {
  if (confirm('Удалить это фото?')) {
    const newPhotos = [...photos.value]
    newPhotos.splice(index, 1)
    photos.value = newPhotos
  }
}

const editPhoto = (index) => {
  editingPhoto.value = index
  editPhotoName.value = photos.value[index].name || ''
}

const closeEditModal = () => {
  editingPhoto.value = null
  editPhotoName.value = ''
}

const savePhotoEdit = () => {
  if (editingPhoto.value !== null) {
    const newPhotos = [...photos.value]
    newPhotos[editingPhoto.value] = {
      ...newPhotos[editingPhoto.value],
      name: editPhotoName.value.trim() || `Фото ${editingPhoto.value + 1}`
    }
    photos.value = newPhotos
    closeEditModal()
  }
}

const handleImageError = (event) => {
  event.target.src = '/placeholder-image.jpg' // Fallback изображение
}
</script>

<style scoped>
.photo-uploader {
  margin-bottom: 1rem;
}

.photos-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #333;
}

.photos-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.photo-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.photo-preview {
  position: relative;
  width: 100%;
  height: 120px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #ddd;
  background: #f8f9fa;
}

.photo-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.photo-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.photo-preview:hover .photo-overlay {
  opacity: 1;
}

.photo-action-btn {
  background: white;
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.photo-action-btn:hover {
  transform: scale(1.1);
}

.edit-btn:hover {
  background: #4caf50;
}

.delete-btn:hover {
  background: #f44336;
}

.photo-name {
  margin-top: 0.5rem;
  font-size: 0.8rem;
  color: #666;
  text-align: center;
  word-break: break-word;
}

.upload-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 2rem;
  border: 2px dashed #ddd;
  border-radius: 8px;
  background: #fafafa;
}

.file-input {
  display: none;
}

.upload-btn {
  padding: 0.8rem 1.5rem;
  background: #4caf50;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.upload-btn:hover {
  background: #45a049;
  transform: translateY(-2px);
}

.limit-info {
  text-align: center;
  color: #666;
  font-size: 0.9rem;
  padding: 1rem;
  background: #f0f0f0;
  border-radius: 8px;
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  max-width: 400px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #eee;
}

.modal-header h4 {
  margin: 0;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: #f0f0f0;
  color: #333;
}

.modal-body {
  padding: 1rem;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #333;
}

.form-group input {
  width: 100%;
  padding: 0.6rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 0.9rem;
}

.modal-footer {
  display: flex;
  gap: 0.5rem;
  padding: 1rem;
  border-top: 1px solid #eee;
}

.btn {
  padding: 0.6rem 1rem;
  border-radius: 8px;
  font-size: 0.9rem;
  cursor: pointer;
  border: none;
  flex: 1;
  transition: all 0.3s ease;
}

.btn-primary {
  background: #4caf50;
  color: white;
}

.btn-outline {
  background: transparent;
  border: 1px solid #ddd;
  color: #666;
}

.btn:hover {
  transform: translateY(-1px);
}

@media (max-width: 768px) {
  .photos-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 0.5rem;
  }
  
  .photo-preview {
    height: 100px;
  }
  
  .upload-section {
    padding: 1rem;
  }
}
</style>
